# Decimal字段分析报告

## 概述
基于对Excel文件`20250610-15销售出库明细账.xlsx`前100行数据的分析，以及SQL文件`wdt_saledetail_create.sql`中定义的decimal字段，发现了以下问题字段。

## 问题字段分类

### 1. 完全为空的字段（100%空值）
这些字段在SQL中定义为decimal类型，但在Excel数据中完全为空：

- **货品成本** - `decimal(10,2) DEFAULT NULL`
- **货品总成本** - `decimal(10,2) DEFAULT NULL`
- **固定成本** - `decimal(10,2) DEFAULT NULL`
- **固定总成本** - `decimal(10,2) DEFAULT NULL`
- **预估邮资** - `decimal(10,2) DEFAULT NULL`
- **邮资成本** - `decimal(10,2) DEFAULT NULL`
- **订单毛利** - `decimal(10,2) DEFAULT NULL`
- **毛利率** - `decimal(10,2) DEFAULT NULL`
- **订单固定毛利** - `decimal(10,2) DEFAULT NULL`
- **固定毛利率** - `decimal(10,2) DEFAULT NULL`

### 2. 包含非数字值的字段
- **订单包装成本** - `decimal(10,2) DEFAULT NULL`
  - 实际数据类型：object (字符串)
  - 所有值都是："无权限"
  - 这是一个严重的数据类型不匹配问题

### 3. 部分为空的字段（15%空值）
这些字段有部分数据为空：

- **货品原单价** - `decimal(10,2) DEFAULT NULL`
  - 空值比例：15.0% (15/100)
  - 非空值为正常的数字类型

- **货品总优惠** - `decimal(10,2) DEFAULT NULL`
  - 空值比例：15.0% (15/100)
  - 非空值为正常的数字类型

- **退款前支付金额** - `decimal(10,2) DEFAULT NULL`
  - 空值比例：15.0% (15/100)
  - 非空值为正常的数字类型

### 4. 数据类型不匹配的字段
虽然这些字段有数据，但数据类型与SQL定义不完全匹配：

- **邮费** - SQL定义为`decimal(10,2)`，Excel中为`int64`
- **货到付款金额** - SQL定义为`decimal(10,2)`，Excel中为`int64`
- **分摊邮费** - SQL定义为`decimal(10,2)`，Excel中为`int64`
- **体积** - SQL定义为`decimal(10,2)`，Excel中为`int64`

## 建议的解决方案

### 1. 对于完全为空的字段
- 确认这些字段是否应该有数据
- 如果确实应该为空，保持SQL中的`DEFAULT NULL`设置
- 考虑在导入时跳过这些字段或设置默认值

### 2. 对于"订单包装成本"字段
- **紧急处理**：这是最严重的问题
- 需要确认"无权限"是否表示某种业务状态
- 建议：
  - 如果"无权限"表示无数据，应转换为NULL
  - 如果"无权限"有特殊含义，考虑将字段类型改为VARCHAR或添加状态字段

### 3. 对于部分为空的字段
- 这些字段的NULL值是正常的，SQL定义已经允许NULL
- 导入时正常处理即可

### 4. 对于数据类型不匹配的字段
- 虽然int可以转换为decimal，但建议：
  - 确认这些字段是否真的需要小数位
  - 如果不需要小数位，考虑将SQL定义改为INT类型
  - 如果需要小数位，保持现有定义，导入时会自动转换

## 导入时需要特别处理的字段

1. **订单包装成本**：需要将"无权限"转换为NULL或特殊值
2. **完全为空的10个字段**：确认是否跳过或设置默认值
3. **部分为空的3个字段**：正常处理NULL值

## 数据质量评估

- **高风险字段**：1个（订单包装成本）
- **中风险字段**：10个（完全为空的字段）
- **低风险字段**：3个（部分为空的字段）
- **正常字段**：其余decimal字段数据质量良好
