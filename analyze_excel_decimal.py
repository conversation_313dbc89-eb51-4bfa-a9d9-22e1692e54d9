import pandas as pd
import numpy as np

# 读取Excel文件的前10行
file_path = 'data/20250610-15销售出库明细账.xlsx'

try:
    # 读取Excel文件 - 读取更多行以获得更全面的分析
    df = pd.read_excel(file_path, nrows=100)
    
    print(f"Excel文件前10行数据（共读取{len(df)}行）：")
    print("=" * 80)
    print(df.head(10))
    
    print("\n\n数据类型信息：")
    print("=" * 80)
    print(df.dtypes)
    
    print("\n\n列名列表：")
    print("=" * 80)
    for i, col in enumerate(df.columns):
        print(f"{i+1:2d}. {col}")
    
    # 从SQL文件中提取decimal字段
    decimal_fields = [
        '货品原单价', '货品原总金额', '订单总优惠', '邮费', '货品成交价', 
        '货品成交总价', '货品总优惠', '货到付款金额', '货品成本', '货品总成本',
        '固定成本', '固定总成本', '订单支付金额', '应收金额', '退款前支付金额',
        '单品支付金额', '分摊邮费', '预估邮资', '邮资成本', '订单包装成本',
        '订单毛利', '毛利率', '订单固定毛利', '固定毛利率', '实际重量', 
        '预估重量', '体积'
    ]
    
    print("\n\nDecimal字段在Excel中的数据类型和样本值：")
    print("=" * 80)
    
    for field in decimal_fields:
        if field in df.columns:
            print(f"\n字段: {field}")
            print(f"数据类型: {df[field].dtype}")
            print(f"非空值数量: {df[field].notna().sum()}/{len(df)}")
            print(f"空值数量: {df[field].isna().sum()}")
            print(f"空值比例: {df[field].isna().sum()/len(df)*100:.1f}%")
            
            # 显示前几个非空值
            non_null_values = df[field].dropna()
            if len(non_null_values) > 0:
                print(f"样本值: {list(non_null_values.head(3))}")
            else:
                print("样本值: 全部为空")
                
            # 检查是否有非数字值
            if len(non_null_values) > 0:
                try:
                    # 尝试转换为数字
                    numeric_values = pd.to_numeric(non_null_values, errors='coerce')
                    non_numeric_count = numeric_values.isna().sum()
                    if non_numeric_count > 0:
                        print(f"非数字值数量: {non_numeric_count}")
                        # 找出非数字值
                        non_numeric_mask = pd.to_numeric(non_null_values, errors='coerce').isna()
                        non_numeric_values = non_null_values[non_numeric_mask]
                        print(f"非数字值样本: {list(non_numeric_values.head(3))}")
                except:
                    print("无法进行数字转换检查")
        else:
            print(f"\n字段: {field} - 在Excel中不存在")

except Exception as e:
    print(f"读取Excel文件时出错: {e}")
